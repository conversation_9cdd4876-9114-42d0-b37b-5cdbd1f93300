<div class="navigation" id="site-navigation">
    <ul class="navigation--mobile flex align-items-m">
        <li class="navigation-logo flex-item-fill">
            <a href="/admin">
                <svg width="32" height="26" viewBox="0 0 32 26" xmlns="http://www.w3.org/2000/svg"><title>GrazeCart</title>
                    <g fill="none" fill-rule="evenodd">
                        <path d="M28 18H11.3l-7-16H0V0h5.6l7 16H28v2" fill="#4D4D4D" />
                        <path d="M18 22.4c0 1.7-1.4 3-3 3-1.7 0-3-1.3-3-3 0-1.6 1.3-3 3-3 1.6 0 3 1.4 3 3zm-1 0c0 1-1 2-2 2s-2-1-2-2 1-2 2-2 2 1 2 2zM14 14h14l.8-2 .8-2 .8-2h-4.8v2h1.7l-.7 2H16.3L14 5H31l.8-2.2H10L14 14M26.7 22.4c0 1.7-1.4 3-3 3-1.7 0-3-1.3-3-3 0-1.6 1.3-3 3-3 1.6 0 3 1.4 3 3zm-1 0c0 1-1 2-2 2s-2-1-2-2 1-2 2-2 2 1 2 2z" fill="#34B393" />
                    </g>
                </svg>
            </a>
        </li>
        <li>
            <button class="btn btn-alt flex-item push-right" @click="toggleNavigation('site-navigation')"><i class="fas fa-bars fa-lg"></i></button>
        </li>
    </ul>
    <ul class="navigation-items">
        <li class="navigation-logo hidden-on-mobile">
            <a href="/admin">
                <svg width="32" height="26" viewBox="0 0 32 26" xmlns="http://www.w3.org/2000/svg"><title>GrazeCart</title>
                    <g fill="none" fill-rule="evenodd">
                        <path d="M28 18H11.3l-7-16H0V0h5.6l7 16H28v2" fill="#4D4D4D" />
                        <path d="M18 22.4c0 1.7-1.4 3-3 3-1.7 0-3-1.3-3-3 0-1.6 1.3-3 3-3 1.6 0 3 1.4 3 3zm-1 0c0 1-1 2-2 2s-2-1-2-2 1-2 2-2 2 1 2 2zM14 14h14l.8-2 .8-2 .8-2h-4.8v2h1.7l-.7 2H16.3L14 5H31l.8-2.2H10L14 14M26.7 22.4c0 1.7-1.4 3-3 3-1.7 0-3-1.3-3-3 0-1.6 1.3-3 3-3 1.6 0 3 1.4 3 3zm-1 0c0 1-1 2-2 2s-2-1-2-2 1-2 2-2 2 1 2 2z" fill="#34B393" />
                    </g>
                </svg>
            </a>
        </li>
        <li class="dropdown">
            <a href="{{ route('admin.orders.index', ['subscription_status' => 'all', 'confirmed' => 1, 'order_status' => [1,8], ]) }}" title="Orders">Orders</a>
        </li>

        <li class="dropdown">
            <a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">Products
                <span class="caret"></span>
            </a>
            <ul class="dropdown-menu">
                <li><a href="/admin/products">All Products</a></li>
                <li><a href="/admin/collections">Collections</a></li>
                <li><a href="/admin/subcollections">Subcollections</a></li>
                <li class="divider"></li>
                <li><a href="/admin/coupons">Coupons</a></li>
                <li><a href="/admin/gift-cards">Gift Cards</a></li>
                <li class="divider"></li>
                <li><a href="/admin/vendors">Vendors</a></li>
                <li><a href="/admin/protocols">Protocols</a></li>
            </ul>
        </li>

        <li class="dropdown">
            <a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">Logistics
                <span class="caret"></span>
            </a>
            <ul class="dropdown-menu">
                <li><a href="/admin/logistics/pickups">Pickup Locations</a></li>
                <li><a href="/admin/logistics/delivery">Delivery Zones</a></li>
                <li><a href="/admin/schedules">Schedules</a></li>
                <li class="divider"></li>
                <li><a href="/admin/proposals">Pickup Proposals</a></li>
                <li><a href="/admin/locations/market">Other Locations</a></li>
            </ul>
        </li>

        <li class="dropdown">
            <a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">Reports
                <span class="caret"></span>
            </a>
            <ul class="dropdown-menu">
                <li><a href="/admin/reports/delivery-sales">Delivery Sales</a></li>
                <li><a href="/admin/reports/product-sales?order_status%5B0%5D=1&order_status%5B1%5D=2&order_status%5B2%5D=3">Product Sales</a></li>
                <li><a href="/admin/reports/product-level-order">Product Level Order</a></li>
                <li><a href="/admin/reports/inventory">Inventory</a></li>
                <li><a href="/admin/reports/stock-out">Stock-out Report</a></li>
            </ul>
        </li>

        <li><a href="/admin/users">Customers</a></li>

        <li class="dropdown">
            <a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">My Site
                <span class="caret"></span>
            </a>
            <ul class="dropdown-menu">
                <li><a href="/admin/themes">Theme</a></li>
                <li><a href="/admin/templates">Message Templates</a></li>
                <li><a href="/admin/menus">Menu Editor</a></li>
                <li><a href="/admin/pages">Pages</a></li>
                <li><a href="/admin/posts">Blog Posts</a></li>
                <li><a href="/admin/recipes">Recipes</a></li>
                <li><a href="/admin/photos">Photo Library</a></li>
                <li class="divider"></li>
                <li><a href="/"><i class="fas fa-external-link fa-fw" aria-hidden="true"></i> Visit Your Site</a></li>
            </ul>
        </li>

        <li>
            <a href="/admin/settings">Settings</a>
        </li>

        <li style="margin-right: auto;">
            <a href="#" @click="showModal('messengerModal')"><i class="fas fa-envelope"></i></a>
        </li>

        <li class="dropdown push-right">
            <a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false"><i class="fas fa-question-circle"></i></a>
            <ul class="dropdown-menu pull-right">
                <li><a href="https://help.grazecart.com" target="_blank">Documentation <i class="far fa-external-link-square fa-fw"></i></a></li>
                <li><a href="https://help.grazecart.com/hc/en-us/requests/new" target="_blank">Support</a></li>
            </ul>
        </li>

        <li class="dropdown">
            <a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false"><i class="fas fa-user fa-fw"></i> {{ auth()->user()['first_name'] ?? '' }}
                <i class="fas fa-caret-down"></i></a>
            <ul class="dropdown-menu pull-right">
                <li><a href="/admin/account"><i class="fas fa-sliders-h-square fa-fw"></i> Account Settings</a></li>
                @if(Gate::allows('update-teams'))
                    <li><a href="/admin/team"><i class="fas fa-users fa-fw"></i> Manage Team</a></li>
                @endif
                <li class="text-muted"><a href="https://help.grazecart.com" target="_blank"><i class="fas fa-question-circle fa-fw" aria-hidden="true"></i>
                        Documentation</a></li>
                <li role="separator" class="divider"></li>
                <li><a href="/admin/logout"><i class="fas fa-sign-out fa-fw"></i> Log Out</a></li>
            </ul>
        </li>
    </ul>
</div>

@extends('layouts.main', ['pageTitle' => 'Aggregate Sale by Order Report | Reports'])

@section('toolbar-breadcrumb')
    <li>Reports</li>
    <li>Aggregate Sale by Order Report ({{ count($results) }})</li>
@endsection

@section('toolbar-buttons')
    <button onclick="window.print()" class="btn btn-default flex-item"><i class="fas fa-print"></i> Print</button>
@endsection

@section('content')
    <div class="visible-print text-center">
        <h5><strong>Aggregate Sale by Order Report</strong></h5>
        @if(session()->has('aggregate-sale-by-order-applied-filters'))
            @foreach(session('aggregate-sale-by-order-applied-filters') as $key => $filterGroup)
                <ul class="list-inline">
                    @foreach($filterGroup as $filter)
                        <li>{{ $filter }}</li>
                    @endforeach
                </ul>
            @endforeach
        @endif
    </div>

    @include('reports.aggregate-sale-by-order.partials.index-table')
@endsection

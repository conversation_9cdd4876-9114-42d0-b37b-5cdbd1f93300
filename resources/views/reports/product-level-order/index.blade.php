@extends('layouts.main', ['pageTitle' => 'Product Level Order Report | Reports'])

@section('toolbar-breadcrumb')
    <li>Reports</li>
    <li>Product Level Order Report ({{ count($results) }})</li>
@endsection

@section('toolbar-buttons')
    <button onclick="window.print()" class="btn btn-default flex-item"><i class="fas fa-print"></i> Print</button>
@endsection

@section('content')
    <div class="visible-print text-center">
        <h5><strong>Product Level Order Report</strong></h5>
        @if(session()->has('product-level-order-applied-filters'))
            @foreach(session('product-level-order-applied-filters') as $key => $filterGroup)
                <ul class="list-inline">
                    @foreach($filterGroup as $filter)
                        <li>{{ $filter }}</li>
                    @endforeach
                </ul>
            @endforeach
        @endif
    </div>

    @include('reports.product-level-order.partials.index-table')
@endsection

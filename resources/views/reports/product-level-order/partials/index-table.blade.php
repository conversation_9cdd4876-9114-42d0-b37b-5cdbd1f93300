<div class="panel panel-default">
    <div class="panel-header">
        <form method="GET" action="{{ route('admin.reports.product-level-order') }}" id="filterReportsForm">
            <div class="flex align-items-m">
                <div class="flex-item-fill">
                    <input 
                        type="text" 
                        class="form-control" 
                        placeholder="Search products by name..." 
                        name="products" 
                        value="{{ Request::get('products') }}"
                    >
                </div>
            </div>
            @include('reports.product-level-order.partials.filter-form')
        </form>
        @include('partials.applied-filters', ['filter_resource' => 'product_level_order_report']) 
    </div>
    <div class="panel-body pa-0">
        <div class="table-responsive">
            <table class="table table-striped table-full table-hover">
                <thead>
                <tr>
                    <th>Order ID</th>
                    <th>Order Type</th>
                    <th>Product Type</th>
                    <th>Billable</th>
                    <th>SKU</th>
                    <th>Product Title</th>
                    <th>Quantity</th>
                    <th>Unit of Issue</th>
                    <th>Packing Group</th>
                    <th class="text-right">Retail Price/Unit</th>
                    <th class="text-right">Billed Price/Unit</th>
                    <th class="text-right">Total Billed</th>
                    <th class="text-right">Discount/Unit</th>
                    <th class="text-right">Total Discount</th>
                    <th>Confirmation Date</th>
                    <th>Delivery Date</th>
                    <th>Location</th>
                    <th>Schedule</th>
                    <th>Customer</th>
                    <th>Customer Email</th>
                    <th>Customer Phone</th>
                </tr>
                </thead>
                <tbody>
                @foreach($results as $result)
                    <tr>
                        <td data-label="Order ID">{{ $result->order_id }}</td>
                        <td data-label="Order Type">{{ $result->order_type }}</td>
                        <td data-label="Product Type">{{ $result->product_type }}</td>
                        <td data-label="Billable">
                            <span class="badge {{ $result->billable === 'Y' ? 'badge-success' : 'badge-secondary' }}">
                                {{ $result->billable === 'Y' ? 'Yes' : 'No' }}
                            </span>
                        </td>
                        <td data-label="SKU">{{ $result->sku }}</td>
                        <td data-label="Product Title">{{ $result->title }}</td>
                        <td data-label="Quantity">{{ $result->quantity }}</td>
                        <td data-label="Unit of Issue">{{ $result->unit_of_issue }}</td>
                        <td data-label="Packing Group">{{ $result->packing_group }}</td>
                        <td data-label="Retail Price/Unit" class="text-right">
                            @if($result->billable === 'Y')
                                ${{ money($result->retail_price_per_unit) }}
                            @else
                                -
                            @endif
                        </td>
                        <td data-label="Billed Price/Unit" class="text-right">
                            @if($result->billable === 'Y')
                                ${{ money($result->billed_price_per_unit) }}
                            @else
                                -
                            @endif
                        </td>
                        <td data-label="Total Billed" class="text-right">
                            @if($result->billable === 'Y')
                                ${{ money($result->total_billed_price) }}
                            @else
                                -
                            @endif
                        </td>
                        <td data-label="Discount/Unit" class="text-right">
                            @if($result->billable === 'Y')
                                ${{ money($result->discount_per_unit) }}
                            @else
                                -
                            @endif
                        </td>
                        <td data-label="Total Discount" class="text-right">
                            @if($result->billable === 'Y')
                                ${{ money($result->total_discount) }}
                            @else
                                -
                            @endif
                        </td>
                        <td data-label="Confirmation Date">{{ $result->confirmation_date ? \Carbon\Carbon::parse($result->confirmation_date)->format('M j, Y') : '' }}</td>
                        <td data-label="Delivery Date">{{ $result->delivery_date ? \Carbon\Carbon::parse($result->delivery_date)->format('M j, Y') : '' }}</td>
                        <td data-label="Location">{{ $result->location_name }}</td>
                        <td data-label="Schedule">{{ $result->schedule_name }}</td>
                        <td data-label="Customer">{{ $result->customer_first_name }} {{ $result->customer_last_name }}</td>
                        <td data-label="Customer Email">{{ $result->customer_email }}</td>
                        <td data-label="Customer Phone">{{ $result->customer_phone }}</td>
                    </tr>
                @endforeach
                </tbody>
                @if(!$results->count())<tr><td colspan="100%">No results found.</td></tr>@endif
            </table>
        </div>
    </div>
</div>

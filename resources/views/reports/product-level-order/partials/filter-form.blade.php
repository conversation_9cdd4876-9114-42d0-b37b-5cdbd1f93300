<div class="filterPanel" id="filterPanel">
    <div class="settingsPanel pa-0">
        <div class="settingsPanel__header flex align-items-m">
            <div class="flex-item-fill">Filter Report</div>
            <button type="button" class="btn btn-alt flex-item" @click="hidePanel('filterPanel')"><i
                        class="fas fa-times fa-lg"></i></button>
        </div>
        <div class="settingsPanel__body">
            <div class="form-group">
                <label for="order_status">Order Status</label>
                <x-form.order-status-select
                        class="form-control select2 autofocus"
                        name="order_status[]"
                        data-placeholder="Select statuses"
                        tabindex="1"
                        multiple
                        :selected="request('order_status')"
                />
            </div>

            {{--SKU--}}
            <div class="form-group">
                <label for="title">Product SKU</label>
                <input type="text" name="sku" class="form-control" value="{{ Request::get('sku') }}"
                       placeholder="Search by product SKU" tabindex="1" />
            </div>

            {{--Location--}}
            <div class="form-group">
                <label for="pickup_id">Pickup Location</label>
                <x-form.delivery-method-select
                        class="form-control select2"
                        name="pickup_id[]"
                        tabindex="1"
                        data-placeholder="Select locations"
                        multiple
                        style="width: 100%"
                        :selected="request('pickup_id')"
                />
            </div>

            <div x-data="dateRange('confirmed_date_start', 'confirmed_date_end')" class="form-group">
                <div class="flex items-center justify-between">
                    <label for="confirmed_date">Order confirmed date</label>
                    <x-form.date-range-dropdown />
                </div>

                <div class="mt-1 flex space-x-2 items-center">

                    <x-form.pikaday-input
                            name="confirmed_date[start]"
                            class="form-control"
                            value="{{ request('confirmed_date')['start'] ?? '' }}"
                            id="confirmed_date_start"
                            placeholder="Start date"
                            tabindex="1"
                    />

                    <x-form.pikaday-input
                            name="confirmed_date[end]"
                            class="form-control"
                            value="{{ request('confirmed_date')['end'] ?? '' }}"
                            id="confirmed_date_end"
                            placeholder="End date"
                            tabindex="1"
                    />

                </div>
            </div>

            <div x-data="dateRange('pickup_date_start', 'pickup_date_end')" class="form-group">
                <div class="flex items-center justify-between">
                    <label for="pickup_date">Order pickup date</label>
                    <x-form.date-range-dropdown />
                </div>

                <div class="mt-1 flex space-x-2 items-center">
                    <x-form.pikaday-input
                            name="pickup_date[start]"
                            class="form-control"
                            value="{{ request('pickup_date')['start'] ?? '' }}"
                            id="pickup_date_start"
                            placeholder="Start date"
                            tabindex="1"
                    />

                    <x-form.pikaday-input
                            name="pickup_date[end]"
                            class="form-control"
                            value="{{ request('pickup_date')['end'] ?? '' }}"
                            id="pickup_date_end"
                            placeholder="End date"
                            tabindex="1"
                    />
                </div>
            </div>

            <div x-data="dateRange('payment_date_start', 'payment_date_end')" class="form-group">
                <div class="flex items-center justify-between">
                    <label for="payment_date">Payment date</label>
                    <x-form.date-range-dropdown />
                </div>

                <div class="mt-1 flex space-x-2 items-center">
                    <x-form.pikaday-input
                            name="payment_date[start]"
                            class="form-control"
                            value="{{ request('payment_date')['start'] ?? '' }}"
                            id="payment_date_start"
                            placeholder="Start date"
                            tabindex="1"
                    />

                    <x-form.pikaday-input
                            name="payment_date[end]"
                            class="form-control"
                            value="{{ request('payment_date')['end'] ?? '' }}"
                            id="payment_date_end"
                            placeholder="End date"
                            tabindex="1"
                    />
                </div>
            </div>

            {{--Packing Group--}}
            <div class="form-group">
                <label for="pacing_group">Packing Group</label>
                <x-form.packing-group-select
                        class="form-control select2"
                        name="inventory_type[]"
                        data-placeholder="Select packing groups"
                        tabindex="1"
                        multiple
                        :selected="request('inventory_type')"
                />
            </div>

            {{--Vendor_id--}}
            <div class="form-group">
                <label for="vendor_id">Vendor</label>
                <x-form.vendor-select
                        class="form-control"
                        name="vendor_id"
                        tabindex="1"
                        placeholder="All"
                        :selected="request('vendor_id')"
                />
            </div>

            {{--Collection_id--}}
            <div class="form-group">
                <label for="collection_id">Collection</label>
                <x-form.collection-select
                        class="form-control select2"
                        name="collection_id[]"
                        multiple
                        :selected="request('collection_id')"
                        :include_all="true"
                />
            </div>

            {{--Schedule--}}
            <div class="form-group">
                <label for="schedule_id">Pickup Schedule</label>
                <x-form.schedule-select
                        class="form-control select2"
                        name="schedule_id[]"
                        tabindex="1"
                        data-placeholder="Select schedules"
                        multiple
                        :selected="request('schedule_id')"
                        :only-active="true"
                />
            </div>

            {{--Customer--}}
            <div class="form-group">
                <label for="title">Customer</label>
                <input type="text" name="customer" class="form-control" value="{{ Request::get('customer') }}"
                       placeholder="Search by customer name, email, phone" tabindex="1" />
            </div>

            {{--Sales Channel--}}
            <div class="form-group">
                <label for="order_type_id">Sales Channel</label>
                <x-form.channel-select
                        class="form-control select2"
                        name="order_type_id[]"
                        tabindex="1"
                        data-placeholder="Select sales channels"
                        multiple
                        :selected="request('order_type_id')"
                        :include_all="true"
                />
            </div>

            {{--Tags--}}
            <div class="form-group">
                <label for="order_tags">Tags</label>
                <x-form.order-tag-select
                        class="form-control select2"
                        name="order_tags[]"
                        data-placeholder="Select some tags"
                        multiple
                        style="width: 100%"
                        :selected="request('order_tags')"
                />
            </div>

            {{--Accounting Class--}}
            <div class="form-group">
                <label for="accounting_class">Accounting Class ID</label>
                <input type="text" name="accounting_class" class="form-control"
                       value="{{ Request::get('accounting_class') }}" tabindex="1" />
            </div>

            <div class="form-group">
                <label for="is_bundle">Show Bundles</label>
                <select class="form-control" name="is_bundle" tabindex="1">
                    <option value="">Show both bundles and none bundles</option>
                    {!! selectOptions([
                    true => 'Bundles only',
                    false => 'No bundles',
                    ], request('is_bundle')) !!}
                </select>
            </div>

            <div class="form-group">
                <label for="packed_by">Packed By</label>
                <x-form.staff-select
                        class="form-control"
                        name="packed_by"
                        placeholder="All"
                        :selected="request('packed_by')"
                />
            </div>
        </div>
        <div class="settingsPanel__footer flex align-items-m">
            <button type="submit" class="btn btn-alt mr-md" name="export" value="true"><i
                        class="fas fa-cloud-download"></i></button>
            <button type="submit" class="btn btn-action btn-block btn-lg"
                    @click="submitForm('filterReportsForm')">Filter
            </button>
        </div>
    </div>
</div>

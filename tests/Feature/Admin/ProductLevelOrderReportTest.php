<?php

namespace Tests\Feature\Admin;

use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class ProductLevelOrderReportTest extends TenantTestCase
{
    #[Test]
    public function a_subscribed_admin_can_visit_the_product_level_order_report_page(): void
    {
        $this->actingAsAdmin()
            ->get(route('admin.reports.product-level-order'))
            ->assertOk();
    }

    #[Test]
    public function a_subscribed_admin_can_export_the_product_level_order_report(): void
    {
        $response = $this->actingAsAdmin()
            ->get(route('admin.reports.product-level-order', ['export' => true]));

        $response->assertOk();
        $response->assertHeader('content-type', 'text/csv; charset=UTF-8');
        $response->assertHeader('content-disposition', 'attachment; filename="product_level_order_report.csv"');
    }

    #[Test]
    public function the_product_level_order_report_can_be_filtered_by_date_range(): void
    {
        $this->actingAsAdmin()
            ->get(route('admin.reports.product-level-order', [
                'confirmed_date' => [
                    'start' => '2025-01-01',
                    'end' => '2025-01-31'
                ]
            ]))
            ->assertOk();
    }

    #[Test]
    public function the_product_level_order_report_can_be_filtered_by_order_status(): void
    {
        $this->actingAsAdmin()
            ->get(route('admin.reports.product-level-order', [
                'order_status' => [1, 2, 3]
            ]))
            ->assertOk();
    }

    #[Test]
    public function the_product_level_order_report_can_be_filtered_by_customer(): void
    {
        $this->actingAsAdmin()
            ->get(route('admin.reports.product-level-order', [
                'customer' => '<EMAIL>'
            ]))
            ->assertOk();
    }
}

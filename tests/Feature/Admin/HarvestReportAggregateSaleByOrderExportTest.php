<?php

namespace Tests\Feature\Admin;

use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class HarvestReportAggregateSaleByOrderExportTest extends TenantTestCase
{
    #[Test]
    public function a_subscribed_admin_can_export_the_aggregate_sale_by_order_report_from_harvest_report(): void
    {
        $response = $this->actingAsAdmin()
            ->get(route('admin.reports.harvest', ['asbo-export' => true]));

        $response->assertOk();
        $response->assertHeader('content-type', 'text/csv; charset=UTF-8');
        $response->assertHeader('content-disposition', 'attachment; filename="aggregate_sale_by_order_report.csv"');
    }

    #[Test]
    public function the_aggregate_sale_by_order_export_can_be_filtered_by_date_range(): void
    {
        $response = $this->actingAsAdmin()
            ->get(route('admin.reports.harvest', [
                'asbo-export' => true,
                'confirmed_date' => [
                    'start' => '2025-01-01',
                    'end' => '2025-01-31'
                ]
            ]));

        $response->assertOk();
        $response->assertHeader('content-type', 'text/csv; charset=UTF-8');
    }

    #[Test]
    public function the_regular_harvest_export_still_works(): void
    {
        $response = $this->actingAsAdmin()
            ->get(route('admin.reports.harvest', ['export' => true]));

        $response->assertOk();
        $response->assertHeader('content-type', 'text/csv; charset=UTF-8');
        $response->assertHeader('content-disposition', 'attachment; filename="bundle_sales_report.csv"');
    }
}

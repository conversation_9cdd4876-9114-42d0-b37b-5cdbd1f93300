<?php

namespace App\Http\Controllers\Admin\Reports;

use App\Exports\AggregateSaleByOrderExport;
use App\Http\Controllers\Controller;
use App\Models\Filter;
use App\Repositories\Reports\ProductLevelOrderReport;
use Illuminate\Http\Request;

class ProductLevelOrderReportController extends Controller
{
    public function index(Request $request)
    {
        $results = (new ProductLevelOrderReport)->query($request);

        if ($request->has('export')) {
            return (new AggregateSaleByOrderExport)->export($results);
        }

        $filters = Filter::where('type', 'product_level_order_report')->get();

        return view('reports.product-level-order.index')
            ->with([
                'savedFilters' => $filters,
                'appliedFilters' => $request->appliedFilters(),
                'appliedFilter' => $filters->where('id', $request->get('filter_id'))->first(),
                'results' => $results,
            ]);
    }
}

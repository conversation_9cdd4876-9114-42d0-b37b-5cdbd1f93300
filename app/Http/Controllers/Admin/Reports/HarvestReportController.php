<?php

namespace App\Http\Controllers\Admin\Reports;

use App\Exports\AggregateSaleByOrderExport;
use App\Exports\HarvestReportExport;
use App\Http\Controllers\Controller;
use App\Models\Filter;
use App\Models\OrderItem;
use App\Models\Product;
use App\Repositories\Reports\HarvestReport;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;

class HarvestReportController extends Controller
{
    public function index(Request $request)
    {
        $results = (new HarvestReport($request))->query()
            ->get()
            ->map(function(OrderItem $baseItem): array {
                if (count($baseItem->bundleProduct) === 0) {
                    return [
                        'product_id' => $baseItem->product_id,
                        /** @phpstan-ignore-next-line  */
                        'count_on_order' => $baseItem->count_on_order,
                        'weight' => $baseItem->weight,
                        'is_base_product' => true,
                    ];

                }
                // Has bundle items.
                return $baseItem->bundleProduct
                    ->map(function ($bundleItem) use ($baseItem) {
                        /** @phpstan-ignore-next-line  */
                        $count_on_order = $baseItem->count_on_order * $bundleItem->qty;
                        $weight = count($baseItem->bundleProduct) === 1
                            ? $baseItem->weight
                            : ($bundleItem->product ? $bundleItem->product->weight * $count_on_order : 0);

                        return [
                            'product_id' => $bundleItem->product_id,
                            'count_on_order' => $count_on_order,
                            'weight' => $weight,
                            'is_base_product' => false,
                        ];
                    })
                    ->toArray();
            })
            ->reduce(function ($carry, $item) {
                if (isset($item['product_id'])) {
                    return $carry->merge([$item]);
                }

                return $carry->merge($item);
            }, collect())
            ->groupBy('product_id')->map(function ($item) {
                return [
                    'count_on_order' => $item->sum('count_on_order'),
                    'weight' => $item->sum('weight'),
                ];
            });

        $products = Product::withTrashed()
            ->whereIn('id', $results->keys())
            ->select(['id', 'title', 'weight', 'sku', 'custom_sort as sort', 'inventory', 'stock_out_inventory', 'barcode'])
            ->get()
            ->map(function (Product $product) use ($results) {
                $product['count_on_order'] = $results->get($product->id)['count_on_order'] ?? 0;
                $product['product_id'] = $product->id;
                $product['weight'] = $results->get($product->id)['weight'];

                return $product;
            });

        if ($orderBy = $request->input('orderBy')) {
            $products = $request->input('sort') === 'asc'
                ? $products->sortBy($orderBy)
                : $products->sortByDesc($orderBy);
        }

        if ($request->has('export')) {
            return (new HarvestReportExport)->export($products->toArray());
        }

        if ($request->filled('asbo-export')) {
            return (new AggregateSaleByOrderExport($results))
                ->download('aggregate_sale_by_order_report.csv', Excel::CSV);
        }

        $filters = Filter::where('type', 'harvest_report')->get();

        return view('reports.harvest.index')
            ->with([
                'savedFilters' => $filters,
                'appliedFilters' => $request->appliedFilters(),
                'appliedFilter' => $filters->where('id', $request->get('filter_id'))->first(),
                'results' => $products,
            ]);
    }
}

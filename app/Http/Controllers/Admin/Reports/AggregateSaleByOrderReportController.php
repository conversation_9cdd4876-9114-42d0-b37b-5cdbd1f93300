<?php

namespace App\Http\Controllers\Admin\Reports;

use App\Exports\AggregateSaleByOrderExport;
use App\Http\Controllers\Controller;
use App\Models\Filter;
use App\Repositories\Reports\AggregateSaleByOrderReport;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;

class AggregateSaleByOrderReportController extends Controller
{
    public function index(Request $request)
    {
        $results = (new AggregateSaleByOrderReport)->query($request);

        if ($request->has('export')) {
            return (new AggregateSaleByOrderExport($results))
                ->download('aggregate_sale_by_order_report.csv', Excel::CSV);
        }

        $filters = Filter::where('type', 'aggregate_sale_by_order_report')->get();

        return view('reports.aggregate-sale-by-order.index')
            ->with([
                'savedFilters' => $filters,
                'appliedFilters' => $request->appliedFilters(),
                'appliedFilter' => $filters->where('id', $request->get('filter_id'))->first(),
                'results' => $results,
            ]);
    }
}

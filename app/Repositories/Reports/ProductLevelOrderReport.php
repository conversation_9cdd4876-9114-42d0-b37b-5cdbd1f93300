<?php

namespace App\Repositories\Reports;

use App\Models\OrderItem;
use App\Models\Pickup;
use App\Models\Product;
use App\Traits\DateRangeTrait;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class ProductLevelOrderReport
{
    use DateRangeTrait;

    protected Request $request;

    public function __construct()
    {
        // Constructor can be empty or used for dependency injection
    }

    /**
     * Get the product-level order data using raw SQL query
     *
     * @param Request $request
     * @return Collection
     */
    public function query(Request $request): Collection
    {
        $this->request = $request;

        // Build the base query conditions
        $whereConditions = $this->buildWhereConditions();

        // Standard products and bundle parent items (billable)
        $standardQuery = "
            SELECT
                o.id AS order_id,
                CASE
                    WHEN o.blueprint_id IS NOT NULL THEN 'Subscription'
                    ELSE 'One Time'
                END AS order_type,
                oi.id AS order_item_id,
                CASE
                    WHEN p.is_bundle = 1 THEN 'Bundle'
                    ELSE 'Standard'
                END AS product_type,
                'Y' AS billable,
                p.barcode AS product_barcode,
                oi.product_id AS product_id,
                p.sku AS sku,
                oi.title AS title,
                p.unit_of_issue AS unit_of_issue,
                oi.fulfilled_qty AS quantity,
                p.custom_sort AS sort_id,
                pg.title AS packing_group,
                p.accounting_class AS accounting_class_id,
                oi.weight AS pounds_per_unit,
                oi.weight * oi.fulfilled_qty AS total_pounds,
                oi.store_price AS retail_price_per_unit,
                oi.store_price * oi.fulfilled_qty AS total_retail_price,
                oi.unit_price AS billed_price_per_unit,
                oi.unit_price * oi.fulfilled_qty AS total_billed_price,
                oi.store_price - oi.unit_price AS discount_per_unit,
                (oi.store_price * oi.fulfilled_qty) - (oi.unit_price * oi.fulfilled_qty) AS total_discount,
                o.confirmed_date AS confirmation_date,
                o.deadline_date AS deadline_date,
                o.pack_deadline_at AS pack_date,
                o.payment_date AS payment_date,
                o.pickup_date AS delivery_date,
                PI.title AS location_name,
                PI.id AS location_id,
                s.title AS schedule_name,
                s.id AS schedule_id,
                o.customer_id AS customer_id,
                o.customer_first_name AS customer_first_name,
                o.customer_last_name AS customer_last_name,
                o.customer_phone AS customer_phone,
                o.customer_email AS customer_email,
                o.shipping_street AS shipping_street,
                o.shipping_street_2 AS shipping_street_2,
                o.shipping_city AS shipping_city,
                o.shipping_state AS shipping_state,
                o.shipping_zip AS shipping_zip,
                'USA' AS shipping_country,
                u.order_count AS customer_order_count,
                u.notes AS profile_notes,
                o.customer_notes AS customer_notes,
                o.packing_notes AS private_notes,
                o.invoice_notes AS invoice_notes,
                o.payment_notes AS payment_notes
            FROM
                order_items oi
                INNER JOIN orders o ON oi.order_id = o.id
                INNER JOIN users u ON o.customer_id = u.id
                INNER JOIN products p ON oi.product_id = p.id
                INNER JOIN packing_groups pg ON p.inventory_type = pg.id
                INNER JOIN pickups PI ON o.pickup_id = PI.id
                INNER JOIN schedules s ON o.schedule_id = s.id
            WHERE
                o.confirmed = 1
                AND o.canceled = 0
                {$whereConditions}
        ";

        // Bundle component products (non-billable)
        $bundleQuery = "
            SELECT
                o.id AS order_id,
                CASE
                    WHEN o.blueprint_id IS NOT NULL THEN 'Subscription'
                    ELSE 'One Time'
                END AS order_type,
                oi.id AS order_item_id,
                CASE
                    WHEN p.is_bundle = 1 THEN 'Bundle'
                    ELSE 'Standard'
                END AS product_type,
                'N' AS billable,
                bp_product.barcode AS product_barcode,
                bp.product_id AS product_id,
                bp_product.sku AS sku,
                bp_product.title AS title,
                bp_product.unit_of_issue AS unit_of_issue,
                oi.fulfilled_qty * bp.qty AS quantity,
                bp_product.custom_sort AS sort_id,
                bp_pg.title AS packing_group,
                bp_product.accounting_class AS accounting_class_id,
                0 AS pounds_per_unit,
                0 AS total_pounds,
                0 AS retail_price_per_unit,
                0 AS total_retail_price,
                0 AS billed_price_per_unit,
                0 AS total_billed_price,
                0 AS discount_per_unit,
                0 AS total_discount,
                o.confirmed_date AS confirmation_date,
                o.deadline_date AS deadline_date,
                o.pack_deadline_at AS pack_date,
                o.payment_date AS payment_date,
                o.pickup_date AS delivery_date,
                PI.title AS location_name,
                PI.id AS location_id,
                s.title AS schedule_name,
                s.id AS schedule_id,
                o.customer_id AS customer_id,
                o.customer_first_name AS customer_first_name,
                o.customer_last_name AS customer_last_name,
                o.customer_phone AS customer_phone,
                o.customer_email AS customer_email,
                o.shipping_street AS shipping_street,
                o.shipping_street_2 AS shipping_street_2,
                o.shipping_city AS shipping_city,
                o.shipping_state AS shipping_state,
                o.shipping_zip AS shipping_zip,
                'USA' AS shipping_country,
                u.order_count AS customer_order_count,
                u.notes AS profile_notes,
                o.customer_notes AS customer_notes,
                o.packing_notes AS private_notes,
                o.invoice_notes AS invoice_notes,
                o.payment_notes AS payment_notes
            FROM
                order_items oi
                INNER JOIN orders o ON oi.order_id = o.id
                INNER JOIN users u ON o.customer_id = u.id
                INNER JOIN products p ON oi.product_id = p.id
                INNER JOIN packing_groups pg ON p.inventory_type = pg.id
                INNER JOIN pickups PI ON o.pickup_id = PI.id
                INNER JOIN schedules s ON o.schedule_id = s.id
                INNER JOIN bundle_product bp ON oi.product_id = bp.bundle_id
                INNER JOIN products bp_product ON bp.product_id = bp_product.id
                INNER JOIN packing_groups bp_pg ON bp_product.inventory_type = bp_pg.id
            WHERE
                o.confirmed = 1
                AND o.canceled = 0
                {$whereConditions}
        ";

        $fullQuery = "({$standardQuery}) UNION ALL ({$bundleQuery}) ORDER BY order_id, order_item_id";

        return collect(DB::select($fullQuery, $this->getBindings()));
    }

    /**
     * Build WHERE conditions based on request filters
     */
    private function buildWhereConditions(): string
    {
        $conditions = [];

        // Default date filter - only show recent orders
        if (!$this->request->filled('confirmed_date')) {
            $conditions[] = "AND o.confirmed_date >= '2025-01-01'";
        }

        // Date range filters
        if ($this->request->filled('confirmed_date')) {
            $range = $this->getDateRange($this->request->get('confirmed_date'));
            if ($start = $range->get('start')) {
                $conditions[] = "AND o.confirmed_date >= ?";
            }
            if ($end = $range->get('end')) {
                $conditions[] = "AND o.confirmed_date <= ?";
            }
        }

        if ($this->request->filled('pickup_date')) {
            $range = $this->getDateRange($this->request->get('pickup_date'));
            if ($start = $range->get('start')) {
                $conditions[] = "AND o.pickup_date >= ?";
            }
            if ($end = $range->get('end')) {
                $conditions[] = "AND o.pickup_date <= ?";
            }
        }

        if ($this->request->filled('payment_date')) {
            $range = $this->getDateRange($this->request->get('payment_date'));
            if ($start = $range->get('start')) {
                $conditions[] = "AND o.payment_date >= ?";
            }
            if ($end = $range->get('end')) {
                $conditions[] = "AND o.payment_date <= ?";
            }
        }

        // Order status filter
        if ($this->request->has('order_status')) {
            $statuses = (array) $this->request->get('order_status');
            $placeholders = str_repeat('?,', count($statuses) - 1) . '?';
            $conditions[] = "AND o.status_id IN ({$placeholders})";
        }

        // Order type filter
        if ($this->request->filled('order_type_id')) {
            $types = (array) $this->request->get('order_type_id');
            $placeholders = str_repeat('?,', count($types) - 1) . '?';
            $conditions[] = "AND o.type_id IN ({$placeholders})";
        }

        // Pickup/Location filter
        if ($this->request->filled('pickup_id')) {
            $pickups = (array) $this->request->get('pickup_id');
            $placeholders = str_repeat('?,', count($pickups) - 1) . '?';
            $conditions[] = "AND o.pickup_id IN ({$placeholders})";
        }

        // Schedule filter
        if ($this->request->filled('schedule_id')) {
            $schedules = (array) $this->request->get('schedule_id');
            $placeholders = str_repeat('?,', count($schedules) - 1) . '?';
            $conditions[] = "AND s.id IN ({$placeholders})";
        }

        // Customer filter
        if ($this->request->filled('customer')) {
            $customer = $this->request->get('customer');
            if (filter_var($customer, FILTER_VALIDATE_INT)) {
                $conditions[] = "AND o.customer_id = ?";
            } elseif (filter_var($customer, FILTER_VALIDATE_EMAIL)) {
                $conditions[] = "AND o.customer_email = ?";
            } else {
                $conditions[] = "AND (CONCAT(o.customer_first_name, ' ', o.customer_last_name) LIKE ? OR o.customer_phone = ? OR o.accounting_id = ?)";
            }
        }

        // Product filters
        if ($this->request->contains(['products', 'sku', 'inventory_type', 'vendor_id', 'is_bundle', 'collection_id', 'accounting_class'])) {
            $productIds = $this->getProductsIds();
            if ($productIds->isNotEmpty()) {
                $placeholders = str_repeat('?,', $productIds->count() - 1) . '?';
                $conditions[] = "AND oi.product_id IN ({$placeholders})";
            }
        }

        return implode(' ', $conditions);
    }

    /**
     * Get parameter bindings for the query
     */
    private function getBindings(): array
    {
        $bindings = [];

        // Date range bindings
        if ($this->request->filled('confirmed_date')) {
            $range = $this->getDateRange($this->request->get('confirmed_date'));
            if ($start = $range->get('start')) {
                $bindings[] = $start;
            }
            if ($end = $range->get('end')) {
                $bindings[] = $end;
            }
        }

        if ($this->request->filled('pickup_date')) {
            $range = $this->getDateRange($this->request->get('pickup_date'));
            if ($start = $range->get('start')) {
                $bindings[] = $start;
            }
            if ($end = $range->get('end')) {
                $bindings[] = $end;
            }
        }

        if ($this->request->filled('payment_date')) {
            $range = $this->getDateRange($this->request->get('payment_date'));
            if ($start = $range->get('start')) {
                $bindings[] = $start;
            }
            if ($end = $range->get('end')) {
                $bindings[] = $end;
            }
        }

        // Order status bindings
        if ($this->request->has('order_status')) {
            $bindings = array_merge($bindings, (array) $this->request->get('order_status'));
        }

        // Order type bindings
        if ($this->request->filled('order_type_id')) {
            $bindings = array_merge($bindings, (array) $this->request->get('order_type_id'));
        }

        // Pickup bindings
        if ($this->request->filled('pickup_id')) {
            $bindings = array_merge($bindings, (array) $this->request->get('pickup_id'));
        }

        // Schedule bindings
        if ($this->request->filled('schedule_id')) {
            $bindings = array_merge($bindings, (array) $this->request->get('schedule_id'));
        }

        // Customer bindings
        if ($this->request->filled('customer')) {
            $customer = $this->request->get('customer');
            if (filter_var($customer, FILTER_VALIDATE_INT) || filter_var($customer, FILTER_VALIDATE_EMAIL)) {
                $bindings[] = $customer;
            } else {
                $bindings[] = "%{$customer}%";
                $bindings[] = $customer;
                $bindings[] = $customer;
            }
        }

        // Product bindings
        if ($this->request->contains(['products', 'sku', 'inventory_type', 'vendor_id', 'is_bundle', 'collection_id', 'accounting_class'])) {
            $productIds = $this->getProductsIds();
            if ($productIds->isNotEmpty()) {
                $bindings = array_merge($bindings, $productIds->toArray());
            }
        }

        return $bindings;
    }

    /**
     * Get filtered product IDs
     */
    private function getProductsIds(): Collection
    {
        return Product::query()
            ->withTrashed()
            ->filter($this->request->only([
                'products', 'sku', 'inventory_type', 'vendor_id', 'is_bundle',
                'collection_id', 'accounting_class'
            ]))
            ->pluck('id');
    }
}

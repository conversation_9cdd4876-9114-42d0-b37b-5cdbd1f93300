<?php

namespace App\Repositories\Reports;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Pickup;
use App\Models\Product;
use App\Traits\DateRangeTrait;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class AggregateSaleByOrderReport
{
    use DateRangeTrait;

    public function __construct(
        protected Request $request
    ) {}

    /**
     * Get the product-level order data using Eloquent query builder
     * 
     * @param Request $request
     * @return Collection
     */
    public function query(Request $request): Collection
    {
        $this->request = $request;

        // Get standard products and bundle parent items (billable)
        $standardItems = $this->getStandardItemsQuery()->get();
        
        // Get bundle component products (non-billable)
        $bundleItems = $this->getBundleComponentsQuery()->get();
        
        // Combine and sort the results
        return $standardItems->concat($bundleItems)
            ->sortBy(['order_id', 'order_item_id']);
    }

    /**
     * Get standard products and bundle parent items (billable)
     * 
     * @return Builder<OrderItem>
     */
    private function getStandardItemsQuery(): Builder
    {
        $hasProductFilters = array_filter($this->request->only([
            'products', 'sku', 'inventory_type', 'vendor_id', 'collection_id', 'accounting_class', 'show_deleted'
        ]));

        $orderIds = $this->getOrdersQuery(Order::query())->limit($limit = 3000)->pluck('id');

        return OrderItem::query()
            ->join('orders as o', 'o.id', '=', 'order_items.order_id')
            ->join('users as u', 'u.id', '=', 'o.customer_id')
            ->join('products as p', 'p.id', '=', 'order_items.product_id')
            ->join('packing_groups as pg', 'pg.id', '=', 'p.inventory_type')
            ->join('pickups as PI', 'PI.id', '=', 'o.pickup_id')
            ->join('schedules as s', 's.id', '=', 'o.schedule_id')
            ->selectRaw(implode(',', [
                'o.id as order_id',
                'CASE WHEN o.blueprint_id IS NOT NULL THEN "Subscription" ELSE "One Time" END as order_type',
                'order_items.id as order_item_id',
                'CASE WHEN p.is_bundle = 1 THEN "Bundle" ELSE "Standard" END as product_type',
                '"Y" as billable',
                'p.barcode as product_barcode',
                'order_items.product_id as product_id',
                'p.sku as sku',
                'order_items.title as title',
                'p.unit_of_issue as unit_of_issue',
                'order_items.fulfilled_qty as quantity',
                'p.custom_sort as sort_id',
                'pg.title as packing_group',
                'p.accounting_class as accounting_class_id',
                'order_items.weight as pounds_per_unit',
                'order_items.weight * order_items.fulfilled_qty as total_pounds',
                'order_items.store_price as retail_price_per_unit',
                'order_items.store_price * order_items.fulfilled_qty as total_retail_price',
                'order_items.unit_price as billed_price_per_unit',
                'order_items.unit_price * order_items.fulfilled_qty as total_billed_price',
                'order_items.store_price - order_items.unit_price as discount_per_unit',
                '(order_items.store_price * order_items.fulfilled_qty) - (order_items.unit_price * order_items.fulfilled_qty) as total_discount',
                'o.confirmed_date as confirmation_date',
                'o.deadline_date as deadline_date',
                'o.pack_deadline_at as pack_date',
                'o.payment_date as payment_date',
                'o.pickup_date as delivery_date',
                'PI.title as location_name',
                'PI.id as location_id',
                's.title as schedule_name',
                's.id as schedule_id',
                'o.customer_id as customer_id',
                'o.customer_first_name as customer_first_name',
                'o.customer_last_name as customer_last_name',
                'o.customer_phone as customer_phone',
                'o.customer_email as customer_email',
                'o.shipping_street as shipping_street',
                'o.shipping_street_2 as shipping_street_2',
                'o.shipping_city as shipping_city',
                'o.shipping_state as shipping_state',
                'o.shipping_zip as shipping_zip',
                '"USA" as shipping_country',
                'u.order_count as customer_order_count',
                'u.notes as profile_notes',
                'o.customer_notes as customer_notes',
                'o.packing_notes as private_notes',
                'o.invoice_notes as invoice_notes',
                'o.payment_notes as payment_notes',
            ]))
            ->when(count($orderIds) === $limit, function ($q) {
                $q->whereHas('order', function ($o) {
                    /** @var Builder<Model> $o */
                    return $this->getOrdersQuery($o);
                });
            })
            ->when(count($orderIds) < $limit, function ($q) use ($orderIds) {
                $q->whereIn('order_items.order_id', $orderIds);
            })
            ->when(count($hasProductFilters), function (Builder $p) {
                $query = $this->getProductsQuery()->getQuery();
                $p->mergeWheres($query->wheres, $query->getRawBindings()['where']);
            });
    }

    /**
     * Get bundle component products (non-billable)
     * 
     * @return Builder<OrderItem>
     */
    private function getBundleComponentsQuery(): Builder
    {
        $hasProductFilters = array_filter($this->request->only([
            'products', 'sku', 'inventory_type', 'vendor_id', 'collection_id', 'accounting_class', 'show_deleted'
        ]));

        $orderIds = $this->getOrdersQuery(Order::query())->limit($limit = 3000)->pluck('id');

        return OrderItem::query()
            ->join('orders as o', 'o.id', '=', 'order_items.order_id')
            ->join('users as u', 'u.id', '=', 'o.customer_id')
            ->join('products as p', 'p.id', '=', 'order_items.product_id')
            ->join('packing_groups as pg', 'pg.id', '=', 'p.inventory_type')
            ->join('pickups as PI', 'PI.id', '=', 'o.pickup_id')
            ->join('schedules as s', 's.id', '=', 'o.schedule_id')
            ->join('bundle_product as bp', 'bp.bundle_id', '=', 'order_items.product_id')
            ->join('products as bp_product', 'bp_product.id', '=', 'bp.product_id')
            ->join('packing_groups as bp_pg', 'bp_pg.id', '=', 'bp_product.inventory_type')
            ->selectRaw(implode(',', [
                'o.id as order_id',
                'CASE WHEN o.blueprint_id IS NOT NULL THEN "Subscription" ELSE "One Time" END as order_type',
                'order_items.id as order_item_id',
                'CASE WHEN p.is_bundle = 1 THEN "Bundle" ELSE "Standard" END as product_type',
                '"N" as billable',
                'bp_product.barcode as product_barcode',
                'bp.product_id as product_id',
                'bp_product.sku as sku',
                'bp_product.title as title',
                'bp_product.unit_of_issue as unit_of_issue',
                'order_items.fulfilled_qty * bp.qty as quantity',
                'bp_product.custom_sort as sort_id',
                'bp_pg.title as packing_group',
                'bp_product.accounting_class as accounting_class_id',
                '0 as pounds_per_unit',
                '0 as total_pounds',
                '0 as retail_price_per_unit',
                '0 as total_retail_price',
                '0 as billed_price_per_unit',
                '0 as total_billed_price',
                '0 as discount_per_unit',
                '0 as total_discount',
                'o.confirmed_date as confirmation_date',
                'o.deadline_date as deadline_date',
                'o.pack_deadline_at as pack_date',
                'o.payment_date as payment_date',
                'o.pickup_date as delivery_date',
                'PI.title as location_name',
                'PI.id as location_id',
                's.title as schedule_name',
                's.id as schedule_id',
                'o.customer_id as customer_id',
                'o.customer_first_name as customer_first_name',
                'o.customer_last_name as customer_last_name',
                'o.customer_phone as customer_phone',
                'o.customer_email as customer_email',
                'o.shipping_street as shipping_street',
                'o.shipping_street_2 as shipping_street_2',
                'o.shipping_city as shipping_city',
                'o.shipping_state as shipping_state',
                'o.shipping_zip as shipping_zip',
                '"USA" as shipping_country',
                'u.order_count as customer_order_count',
                'u.notes as profile_notes',
                'o.customer_notes as customer_notes',
                'o.packing_notes as private_notes',
                'o.invoice_notes as invoice_notes',
                'o.payment_notes as payment_notes',
            ]))
            ->when(count($orderIds) === $limit, function ($q) {
                $q->whereHas('order', function ($o) {
                    /** @var Builder<Model> $o */
                    return $this->getOrdersQuery($o);
                });
            })
            ->when(count($orderIds) < $limit, function ($q) use ($orderIds) {
                $q->whereIn('order_items.order_id', $orderIds);
            })
            ->when(count($hasProductFilters), function (Builder $p) {
                $query = $this->getProductsQuery()->getQuery();
                $p->mergeWheres($query->wheres, $query->getRawBindings()['where']);
            });
    }

    /**
     * @return Builder<Order>
     */
    public function getOrdersQuery(Builder $builder): Builder
    {
        return $builder
            ->where('orders.confirmed', true)
            ->where('orders.canceled', false)
            ->when(!$this->request->filled('confirmed_date'), function ($q) {
                // Default date filter - only show recent orders
                return $q->where('orders.confirmed_date', '>=', '2025-01-01');
            })
            ->whereIn('orders.status_id', $this->request->get('order_status', [1, 2, 3]))
            ->when($this->request->filled('order_type_id'), function ($q) {
                return $q->whereIn('type_id', (array) $this->request->get('order_type_id'));
            })
            ->when($this->request->filled('packed_by'), function ($q) {
                return $q->where('staff_id', (int) $this->request->get('packed_by'));
            })
            ->when($this->request->filled('pickup_id') || $this->request->filled('schedule_id'), function ($q) {
                if ($this->request->filled('pickup_id')) {
                    return $q->whereIn('orders.pickup_id', $this->request->get('pickup_id'));
                }

                /** @var array<int, int> $schedule_ids */
                $schedule_ids = $this->request->get('schedule_id', []);

                return $q->whereIn(
                    'orders.pickup_id',
                    Pickup::query()
                        ->whereIn('schedule_id', collect($schedule_ids))
                        ->pluck('id')
                );
            })
            ->when($this->request->filled('pickup_date'), function ($q) {
                $pickup_date_range = $this->getDateRange($this->request->get('pickup_date'));

                $start = $pickup_date_range->get('start');
                if (!is_null($start)) {
                    $q->where('orders.pickup_date', '>=', $start);
                }

                $end = $pickup_date_range->get('end');
                if (!is_null($end)) {
                    $q->where('orders.pickup_date', '<=', $end);
                }

                return $q;
            })
            ->when($this->request->filled('pack_deadline_at'), function ($q) {
                $pack_deadline_at_range = $this->getDateRange($this->request->get('pack_deadline_at'));

                $start = $pack_deadline_at_range->get('start');
                if (!is_null($start)) {
                    $q->where('orders.pack_deadline_at', '>=', Carbon::parse($start)->startOfDay());
                }

                $end = $pack_deadline_at_range->get('end');
                if (!is_null($end)) {
                    $q->where('orders.pack_deadline_at', '<=', Carbon::parse($end)->endOfDay());
                }

                return $q;
            })
            ->when($this->request->filled('confirmed_date'), function ($q) {
                $confirmed_date_range = $this->getDateRange($this->request->get('confirmed_date'));

                $start = $confirmed_date_range->get('start');
                if (!is_null($start)) {
                    $q->where('orders.confirmed_date', '>=', $start);
                }

                $end = $confirmed_date_range->get('end');
                if (!is_null($end)) {
                    $q->where('orders.confirmed_date', '<=', $end);
                }

                return $q;
            })
            ->when($this->request->filled('payment_date'), function ($q) {
                $payment_date_range = $this->getDateRange($this->request->get('payment_date'));

                $start = $payment_date_range->get('start');
                if (!is_null($start)) {
                    $q->where('orders.payment_date', '>=', $start);
                }

                $end = $payment_date_range->get('end');
                if (!is_null($end)) {
                    $q->where('orders.payment_date', '<=', $end);
                }

                return $q;
            })
            ->when($this->request->filled('order_tags'), function ($q) {
                return $q->whereIn('orders.id', function (\Illuminate\Database\Query\Builder $query) {
                    $query->select('order_id')
                        ->from('order_tag')
                        ->whereIn('tag_id', $this->request->get('order_tags', []));
                });
            })->when($this->request->filled('customer'), function ($q) {
                return $this->getCustomerQuery($this->request->get('customer'), $q);
            });
    }

    /**
     * @return Builder<Order>
     */
    private function getCustomerQuery(string $term, Builder $query): Builder
    {
        if (filter_var($term, FILTER_VALIDATE_INT)) {
            return $query->where('orders.customer_id', $term);
        }

        if (filter_var($term, FILTER_VALIDATE_EMAIL)) {
            return $query->where('orders.customer_email', $term);
        }

        return $query->where(function ($query) use ($term) {
            $query->where(DB::raw('CONCAT(customer_first_name, " ", customer_last_name)'), 'LIKE', '%' . $term . '%')
                ->orWhere('customer_phone', $term)
                ->orWhere('accounting_id', $term);
        });
    }

    /**
     * @return Builder<Product>
     */
    private function getProductsQuery(): Builder
    {
        return Product::query()
            ->filter($this->request->only([
                'products', 'sku', 'inventory_type', 'vendor_id', 'collection_id', 'accounting_class', 'show_deleted'
            ]));
    }
}

<?php

namespace App\Exports;

use Illuminate\Support\Collection;

class ProductLevelOrderExport
{
    protected $fileName = 'product_level_order_report.csv';

    public function export(Collection $results)
    {
        $rowHeaders = [
            'order_id',
            'order_type',
            'order_item_id',
            'product_type',
            'billable',
            'product_barcode',
            'product_id',
            'sku',
            'title',
            'unit_of_issue',
            'quantity',
            'sort_id',
            'packing_group',
            'accounting_class_id',
            'pounds_per_unit',
            'total_pounds',
            'retail_price_per_unit',
            'total_retail_price',
            'billed_price_per_unit',
            'total_billed_price',
            'discount_per_unit',
            'total_discount',
            'confirmation_date',
            'deadline_date',
            'pack_date',
            'payment_date',
            'delivery_date',
            'location_name',
            'location_id',
            'schedule_name',
            'schedule_id',
            'customer_id',
            'customer_first_name',
            'customer_last_name',
            'customer_phone',
            'customer_email',
            'shipping_street',
            'shipping_street_2',
            'shipping_city',
            'shipping_state',
            'shipping_zip',
            'shipping_country',
            'customer_order_count',
            'profile_notes',
            'customer_notes',
            'private_notes',
            'invoice_notes',
            'payment_notes',
        ];

        return response()->stream(function () use ($rowHeaders, $results) {
            $export = fopen('php://output', 'w');
            fputcsv($export, $rowHeaders);
            
            foreach ($results as $row) {
                fputcsv($export, [
                    $row->order_id,
                    $row->order_type,
                    $row->order_item_id,
                    $row->product_type,
                    $row->billable,
                    $row->product_barcode,
                    $row->product_id,
                    $row->sku,
                    $row->title,
                    $row->unit_of_issue,
                    $row->quantity,
                    $row->sort_id,
                    $row->packing_group,
                    $row->accounting_class_id,
                    $row->pounds_per_unit,
                    $row->total_pounds,
                    money($row->retail_price_per_unit),
                    money($row->total_retail_price),
                    money($row->billed_price_per_unit),
                    money($row->total_billed_price),
                    money($row->discount_per_unit),
                    money($row->total_discount),
                    $row->confirmation_date,
                    $row->deadline_date,
                    $row->pack_date,
                    $row->payment_date,
                    $row->delivery_date,
                    $row->location_name,
                    $row->location_id,
                    $row->schedule_name,
                    $row->schedule_id,
                    $row->customer_id,
                    $row->customer_first_name,
                    $row->customer_last_name,
                    $row->customer_phone,
                    $row->customer_email,
                    $row->shipping_street,
                    $row->shipping_street_2,
                    $row->shipping_city,
                    $row->shipping_state,
                    $row->shipping_zip,
                    $row->shipping_country,
                    $row->customer_order_count,
                    $row->profile_notes,
                    $row->customer_notes,
                    $row->private_notes,
                    $row->invoice_notes,
                    $row->payment_notes,
                ]);
            }
            fclose($export);
        }, 200, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $this->fileName . '"',
        ]);
    }
}
